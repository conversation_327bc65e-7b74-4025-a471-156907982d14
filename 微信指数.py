import requests
import json
import csv
import os
from datetime import datetime, timed<PERSON><PERSON>


def get_wechat_index_data(openid, search_key, query_keyword):
    """获取微信指数数据"""
    url = "https://search.weixin.qq.com/cgi-bin/wxaweb/wxindex"

    # 计算日期范围：今天和去年的今天
    today = datetime.now()
    last_year = today - timedelta(days=365)

    # 格式化日期为YYYYMMDD
    start_ymd = last_year.strftime("%Y%m%d")
    end_ymd = today.strftime("%Y%m%d")

    print(f"数据日期范围: {start_ymd} 至 {end_ymd}")

    payload = {
        "openid": openid,
        "search_key": search_key,
        "cgi_name": "GetDefaultIndex",
        "query": [query_keyword],
        "compound_word": [],
        "start_ymd": start_ymd,
        "end_ymd": end_ymd
    }

    headers = {
        'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf254061a) XWEB/16133",
        'Content-Type': "application/json",
        'xweb_xhr': "1",
        'Sec-Fetch-Site': "cross-site",
        'Sec-Fetch-Mode': "cors",
        'Sec-Fetch-Dest': "empty",
        'Referer': "https://servicewechat.com/wxc026e7662ec26a3a/56/page-frame.html",
        'Accept-Language': "zh-CN,zh;q=0.9"
    }

    try:
        # 发送API请求
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # 解析响应数据
        data = response.json()
        if data.get('code') != 0:
            raise ValueError(f"API返回错误码: {data.get('code')}")

        # 提取指数数据
        resp_list = data['content']['resp_list']
        if not resp_list:
            raise ValueError("未获取到指数数据")

        return resp_list

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        return None
    except json.JSONDecodeError:
        print("JSON解析失败")
        return None


def convert_to_csv(resp_list, query_keyword, output_folder):
    """将API响应转换为CSV文件，并保存到指定文件夹"""
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"已创建文件夹: {output_folder}")

    # 根据关键词生成文件名和路径
    filename = f"{query_keyword}-微信指数.csv"
    filepath = os.path.join(output_folder, filename)

    with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
        csv_writer = csv.writer(csvfile)

        # 写入表头
        csv_writer.writerow(['关键词', '日期', '微信指数'])

        # 遍历并提取数据
        for resp in resp_list:
            query = resp.get('query', 'N/A')
            for index in resp.get('indexes', []):
                for time_index in index.get('time_indexes', []):
                    date_str = str(time_index['time'])
                    formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
                    score = time_index['score']

                    # 写入CSV行
                    csv_writer.writerow([query, formatted_date, score])

    # 返回文件路径
    return os.path.abspath(filepath)


if __name__ == "__main__":
    # 用户凭证信息
    openid = "ov4ns0FgNTeExCf3cuO4OCtdKNqo"
    search_key = "1754237213621215_3377218506"
    query_keyword = "3"  # 查询关键词

    # 指定输出文件夹
    output_folder = "微信指数"

    print("开始获取微信指数数据...")
    resp_list = get_wechat_index_data(openid, search_key, query_keyword)

    if resp_list:
        print("成功获取数据，开始转换为CSV...")
        csv_file = convert_to_csv(resp_list, query_keyword, output_folder)
        print(f"转换完成！CSV文件已保存至: {csv_file}")

        # 数据统计
        total_count = 0
        min_date = None
        max_date = None

        for resp in resp_list:
            for index in resp.get('indexes', []):
                for time_index in index.get('time_indexes', []):
                    total_count += 1
                    date_str = str(time_index['time'])
                    if not min_date or date_str < min_date:
                        min_date = date_str
                    if not max_date or date_str > max_date:
                        max_date = date_str

        # 格式化日期
        min_date_fmt = f"{min_date[:4]}-{min_date[4:6]}-{min_date[6:]}" if min_date else "N/A"
        max_date_fmt = f"{max_date[:4]}-{max_date[4:6]}-{max_date[6:]}" if max_date else "N/A"

        print(f"数据范围: {min_date_fmt} 至 {max_date_fmt}")
        print(f"数据总数: {total_count}条")
    else:
        print("未能获取有效数据")