from pytrends.request import TrendReq
import pandas as pd

# 连接到 Google Trends（时区设为 UTC+0）
pytrends = TrendReq(hl='zh-CN', tz=480)

# 定义关键词和时间范围
keywords = ["Python programming"]
timeframe = 'now 7-d'  # 或 '2023-08-04 2024-08-04'

# 构建请求
pytrends.build_payload(
    keywords,
    cat=0,
    timeframe=timeframe,
    geo='',
    gprop=''
)



# 获取数据
data = pytrends.interest_over_time()

# 打印前5行和后5行
print(data.head())
print(data.tail())

# 保存到 CSV
data.to_csv('谷歌指数.csv')