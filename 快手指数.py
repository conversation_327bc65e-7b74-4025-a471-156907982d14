import requests
import json


def get_kuaishou_index_data(keywords, cookie):
    """获取快手指数数据"""
    url = "https://index.e.kuaishou.com/rest/index/detail/keyword-valid"

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "ktrace-str": "3|My43ODY4Njk4ODQ1NzU3MDg4LjU0MjY0MzM2LjE3NTQzNzU4MTYzMjguMTA3OQ==|My43ODY4Njk4ODQ1NzU3MDg4LjQzODE3NTgxLjE3NTQzNzU4MTYzMjguMTA3OA==|0|ad-ks-index-kfx|ad|true|src:Js,seqn:8401,rsi:17c888a0-acdf-4cbc-b35f-f566cbf93465,path:/zhishu/keywordAnalysis,rpi:74b7612e00",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "cookie": cookie,
        # "Referer": "https://index.e.kuaishou.com/zhishu/keywordAnalysis?keywordId=219779,673614,423349,21248,695520&keyword=%E4%BA%8C%E6%AC%A1%E5%85%83%2C%E8%83%A1%E6%AD%8C%2C%E8%83%A1%E6%AD%8C%E7%94%B7%E7%A5%9E%2C%E8%83%A1%E6%AD%8C%E7%BB%93%E5%A9%9A%2C%E5%BD%B1%E8%A7%86"
    }

    payload = {
        "keywords": keywords
    }

    try:
        print(f"正在查询关键词: {', '.join(keywords)}")

        # 发送API请求
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # 解析响应数据
        response_data = response.json()
        print(f"API响应状态: {response.status_code}")

        # 直接返回data字段，供下一步请求使用
        if response_data and response_data.get('success') and response_data.get('data'):
            return response_data['data']
        else:
            print(f"API返回错误: {response_data}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        return None
    except json.JSONDecodeError:
        print("JSON解析失败")
        return None



if __name__ == "__main__":
    # Cookie配置 - 需要从浏览器获取最新的cookie
    cookie = "kGateway-identity=kGateway-d65212a1-1264167144; weblogger_did=web_3886670404BAFCB1; did=web_2cd75a8fc11523de96323905f76c14fc592d; userId=43424604; kuaishou.ad.ksIndex_st=ChZrdWFpc2hvdS5hZC5rc0luZGV4LnN0ErABgx1CHOd_PGe4nKOyK_ICzoXjm0XZ08IeZaIT2jjYlsi-HSgvTppoZH2eDzcVO8g5m9VK062Rcq0cfF96mq0NIzZT6Ja4D2tlN37p2EKA3CnhClp7Mphk6wymBQWbFO6ZECJftiego3jD7sVgFWaFm3QnR1WVXAX3cqAGU-QfzBhDan9fsnfWiHyJKbDc1L3cYZvq5wOzky1DtrB-vAfWY5tEi-5oZmaaqE57bkc9aBoaEp09kqfaxOaT_00X8MODjpRE1SIgdN0CMgs5SWnc3oKwBjT1yXsNuM8fIzD2S04nVLUHOzMoBTAB; kuaishou.ad.ksIndex_ph=46476f8ae56fc5752529760b67cabeddacc5; Hm_lvt_55b6f6890a6937842cef785d95ea99d7=**********,**********; Hm_lpvt_55b6f6890a6937842cef785d95ea99d7=**********; HMACCOUNT=4B4FEA3A0636DF36; Hm_lvt_ed0a6497a1fdcdb3cdca291a7692408d=**********,**********; Hm_lpvt_ed0a6497a1fdcdb3cdca291a7692408d=**********; Hm_lvt_2f06440050c04107e4de7a8003748f65=**********,**********; Hm_lpvt_2f06440050c04107e4de7a8003748f65=**********"

    # 关键词配置
    keywords = ["二次元", "胡歌", "胡歌男神", "胡歌结婚", "影视"]

    print("开始获取快手指数数据...")
    data = get_kuaishou_index_data(keywords, cookie)

    if data:
        print(f"成功获取数据，共 {len(data)} 个关键词:")
        for item in data:
            print(f"  关键词ID: {item.get('keywordId')}, 关键词: {item.get('keywordLabel')}")

        print(f"\n数据已准备就绪，可用于下一步请求")
        # data 现在包含关键词信息，可以用于后续API调用

    else:
        print("未能获取有效数据")
        print("\n提示:")
        print("1. 请检查cookie是否有效（可能需要重新从浏览器获取）")
        print("2. 请确认网络连接正常")
        print("3. 请检查关键词是否正确")